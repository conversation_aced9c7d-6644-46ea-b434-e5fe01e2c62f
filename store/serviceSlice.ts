import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { fetchGroups } from "./groupSlice";

/**
 * State for Service slice.
 */
export interface ServiceState {
    services: IStoreService[];
    loading: boolean;
    error: string | null;
    editServiceStatus: 'idle' | 'pending' | 'succeeded' | 'failed';
    editServiceError: string | null;
    uploadServiceImageStatus: 'idle' | 'pending' | 'succeeded' | 'failed';
    uploadServiceImageError: string | null;
    deleteServiceStatus: 'idle' | 'pending' | 'succeeded' | 'failed';
    deleteServiceError: string | null;
    alterServicePositionStatus: 'idle' | 'pending' | 'succeeded' | 'failed';
    alterServicePositionError: string | null;
}

const initialState: ServiceState = {
    services: [],
    loading: false,
    error: null,
    editServiceStatus: 'idle',
    editServiceError: null,
    uploadServiceImageStatus: 'idle',
    uploadServiceImageError: null,
    deleteServiceStatus: 'idle',
    deleteServiceError: null,
    alterServicePositionStatus: 'idle',
    alterServicePositionError: null,
};

/**
 * Async thunk to fetch all services.
 */
export const fetchServices = createAsyncThunk<IStoreService[], void, { rejectValue: string }>(
    "service/fetchServices",
    async (_, { rejectWithValue }) => {
        try {
            const res = await fetch("/api/service");
            if (!res.ok) throw new Error("Failed to fetch services");
            const data = await res.json() as IStoreService[];
            return data;
        } catch (err: any) {
            return rejectWithValue(err.message || "Unknown error");
        }
    }
);

/**
 * Async thunk to edit a service.
 */
export const editService = createAsyncThunk<
    void,
    {
        id: string;
        displayName?: string;
        displayDescription?: string;
        duration?: number;
        price?: number;
        image?: string;
        hidden?: boolean;
    },
    { rejectValue: string }
>(
    "service/editService",
    async (payload, { rejectWithValue, dispatch }) => {
        try {
            const res = await fetch("/api/service", {
                method: "PUT",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(payload),
            });
            if (!res.ok) throw new Error("Failed to edit service");
            dispatch(fetchGroups());
        } catch (err: any) {
            return rejectWithValue(err.message || "Unknown error");
        }
    }
);

/**
 * Async thunk to upload a service image.
 */
export const uploadServiceImage = createAsyncThunk<
    { url: string; id: string },
    { file: File; id: string },
    { rejectValue: string }
>(
    "service/uploadServiceImage",
    async ({ file, id }, { rejectWithValue }) => {
        try {
            const formData = new FormData();
            formData.append("file", file);
            formData.append("id", id);
            const res = await fetch("/api/service/image", {
                method: "POST",
                body: formData,
            });
            if (!res.ok) throw new Error("Failed to upload image");
            const data: any = await res.json();
            return { url: data.url, id: data.id };
        } catch (err: any) {
            return rejectWithValue(err.message || "Unknown error");
        }
    }
);

/**
 * Async thunk to delete (hide) a service.
 */
export const deleteService = createAsyncThunk<
    void,
    { id: string },
    { rejectValue: string }
>(
    "service/deleteService",
    async ({ id }, { rejectWithValue, dispatch }) => {
        try {
            const res = await fetch("/api/service", {
                method: "DELETE",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ id }),
            });
            if (!res.ok) throw new Error("Failed to delete service");
            dispatch(fetchGroups());
        } catch (err: any) {
            return rejectWithValue(err.message || "Unknown error");
        }
    }
);

/**
 * Async thunk to alter the position of a service.
 */
export const alterServicePosition = createAsyncThunk<
    void,
    { displayOrder: number; id: string },
    { rejectValue: string }
>(
    "service/alterServicePosition",
    async ({ displayOrder, id }, { rejectWithValue, dispatch }) => {
        try {
            const res = await fetch("/api/service", {
                method: "PUT",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ displayOrder, id }),
            });
            if (!res.ok) throw new Error("Failed to alter service position");
            dispatch(fetchGroups());
        } catch (err: any) {
            return rejectWithValue(err.message || "Unknown error");
        }
    }
);

export const serviceSlice = createSlice({
    name: "service",
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchServices.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchServices.fulfilled, (state, action: PayloadAction<IStoreService[]>) => {
                state.services = action.payload;
                state.loading = false;
            })
            .addCase(fetchServices.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || "Failed to fetch services";
            })
            // editService
            .addCase(editService.pending, (state) => {
                state.editServiceStatus = 'pending';
                state.editServiceError = null;
            })
            .addCase(editService.fulfilled, (state) => {
                state.editServiceStatus = 'succeeded';
            })
            .addCase(editService.rejected, (state, action) => {
                state.editServiceStatus = 'failed';
                state.editServiceError = action.payload || 'Failed to edit service';
            })
            // uploadServiceImage
            .addCase(uploadServiceImage.pending, (state) => {
                state.uploadServiceImageStatus = 'pending';
                state.uploadServiceImageError = null;
            })
            .addCase(uploadServiceImage.fulfilled, (state) => {
                state.uploadServiceImageStatus = 'succeeded';
            })
            .addCase(uploadServiceImage.rejected, (state, action) => {
                state.uploadServiceImageStatus = 'failed';
                state.uploadServiceImageError = action.payload || 'Failed to upload image';
            })
            // deleteService
            .addCase(deleteService.pending, (state) => {
                state.deleteServiceStatus = 'pending';
                state.deleteServiceError = null;
            })
            .addCase(deleteService.fulfilled, (state) => {
                state.deleteServiceStatus = 'succeeded';
            })
            .addCase(deleteService.rejected, (state, action) => {
                state.deleteServiceStatus = 'failed';
                state.deleteServiceError = action.payload || 'Failed to delete service';
            })
            // alterServicePosition
            .addCase(alterServicePosition.pending, (state) => {
                state.alterServicePositionStatus = 'pending';
                state.alterServicePositionError = null;
            })
            .addCase(alterServicePosition.fulfilled, (state) => {
                state.alterServicePositionStatus = 'succeeded';
            })
            .addCase(alterServicePosition.rejected, (state, action) => {
                state.alterServicePositionStatus = 'failed';
                state.alterServicePositionError = action.payload || 'Failed to alter service position';
            });
    },
});

export default serviceSlice.reducer; 
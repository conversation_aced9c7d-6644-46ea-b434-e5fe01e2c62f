import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { Appointment, Patient, AppointmentsResponse } from "@/types/booking";

/**
 * State for the appointments slice
 */
export interface AppointmentState {
  appointments: Appointment[];
  patient: Patient | null;
  loading: boolean;
  error: string | null;
  cancelLoading: boolean;
  cancelError: string | null;
  rescheduleLoading: boolean;
  rescheduleError: string | null;
}

const initialState: AppointmentState = {
  appointments: [],
  patient: null,
  loading: false,
  error: null,
  cancelLoading: false,
  cancelError: null,
  rescheduleLoading: false,
  rescheduleError: null,
};

/**
 * Async thunk to fetch user appointments
 */
export const fetchAppointments = createAsyncThunk<
  AppointmentsResponse,
  { email?: string; phone?: string },
  { rejectValue: string }
>(
  "appointments/fetchAppointments",
  async ({ email, phone }, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();
      if (email) params.append('email', email);
      if (phone) params.append('phone', phone);

      const response = await fetch(`/api/booking/appointments?${params}`);
      if (!response.ok) {
        if (response.status === 404) {
          return { appointments: [], patient: null } as unknown as AppointmentsResponse;
        }
        throw new Error("Failed to fetch appointments");
      }

      const data = await response.json() as AppointmentsResponse;
      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      return rejectWithValue(errorMessage);
    }
  }
);

/**
 * Async thunk to cancel an appointment
 */
export const cancelAppointment = createAsyncThunk<
  string,
  { appointmentId: string; reason?: string },
  { rejectValue: string }
>(
  "appointments/cancelAppointment",
  async ({ appointmentId, reason }, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/booking/appointments/${appointmentId}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ reason }),
      });

      if (!response.ok) {
        throw new Error("Failed to cancel appointment");
      }

      return appointmentId;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      return rejectWithValue(errorMessage);
    }
  }
);

/**
 * Async thunk to reschedule an appointment
 */
export const rescheduleAppointment = createAsyncThunk<
  Appointment,
  { 
    appointmentId: string; 
    newSlotDate: Date; 
    newDuration?: number;
    reason?: string;
  },
  { rejectValue: string }
>(
  "appointments/rescheduleAppointment",
  async ({ appointmentId, newSlotDate, newDuration, reason }, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/booking/appointments/${appointmentId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          slotDate: newSlotDate.toISOString(),
          duration: newDuration,
          reason,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to reschedule appointment");
      }

      const data = await response.json() as { appointment: Appointment };
      return data.appointment;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      return rejectWithValue(errorMessage);
    }
  }
);

/**
 * Async thunk to update patient information
 */
export const updatePatientInfo = createAsyncThunk<
  Patient,
  {
    patientId: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
  },
  { rejectValue: string }
>(
  "appointments/updatePatientInfo",
  async (patientData, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/booking/patients/${patientData.patientId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(patientData),
      });

      if (!response.ok) {
        throw new Error("Failed to update patient information");
      }

      const data = await response.json() as { patient: Patient };
      return data.patient;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      return rejectWithValue(errorMessage);
    }
  }
);

/**
 * Appointments slice
 */
export const appointmentSlice = createSlice({
  name: "appointments",
  initialState,
  reducers: {
    // Clear appointments data
    clearAppointments: (state) => {
      state.appointments = [];
      state.patient = null;
    },

    // Update appointment status locally (optimistic update)
    updateAppointmentStatus: (state, action: PayloadAction<{ id: string; status: Appointment['status'] }>) => {
      const { id, status } = action.payload;
      const appointment = state.appointments.find(apt => apt.id === id);
      if (appointment) {
        appointment.status = status;
      }
    },

    // Clear errors
    clearErrors: (state) => {
      state.error = null;
      state.cancelError = null;
      state.rescheduleError = null;
    },

    // Set patient data
    setPatient: (state, action: PayloadAction<Patient>) => {
      state.patient = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch appointments
      .addCase(fetchAppointments.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAppointments.fulfilled, (state, action) => {
        state.loading = false;
        state.appointments = action.payload.appointments;
        state.patient = action.payload.patient;
      })
      .addCase(fetchAppointments.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload ?? "Failed to fetch appointments";
      })

      // Cancel appointment
      .addCase(cancelAppointment.pending, (state) => {
        state.cancelLoading = true;
        state.cancelError = null;
      })
      .addCase(cancelAppointment.fulfilled, (state, action) => {
        state.cancelLoading = false;
        // Remove cancelled appointment from the list
        state.appointments = state.appointments.filter(apt => apt.id !== action.payload);
      })
      .addCase(cancelAppointment.rejected, (state, action) => {
        state.cancelLoading = false;
        state.cancelError = action.payload ?? "Failed to cancel appointment";
      })

      // Reschedule appointment
      .addCase(rescheduleAppointment.pending, (state) => {
        state.rescheduleLoading = true;
        state.rescheduleError = null;
      })
      .addCase(rescheduleAppointment.fulfilled, (state, action) => {
        state.rescheduleLoading = false;
        // Update the rescheduled appointment
        const index = state.appointments.findIndex(apt => apt.id === action.payload.id);
        if (index !== -1) {
          state.appointments[index] = action.payload;
        }
      })
      .addCase(rescheduleAppointment.rejected, (state, action) => {
        state.rescheduleLoading = false;
        state.rescheduleError = action.payload ?? "Failed to reschedule appointment";
      })

      // Update patient info
      .addCase(updatePatientInfo.fulfilled, (state, action) => {
        state.patient = action.payload;
      });
  },
});

export const {
  clearAppointments,
  updateAppointmentStatus,
  clearErrors,
  setPatient,
} = appointmentSlice.actions;

export default appointmentSlice.reducer;

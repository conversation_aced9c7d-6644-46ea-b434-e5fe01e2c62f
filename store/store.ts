import { configureStore } from '@reduxjs/toolkit';
import { useDispatch, useSelector } from "react-redux";
import authReducer from './authSlice';
import serviceReducer from "./serviceSlice";
import groupReducer from "./groupSlice";
import bookingReducer from "./bookingSlice";
import appointmentReducer from "./appointmentSlice";

/**
 * Configures the Redux store for the application.
 *
 * @returns {ReturnType<typeof configureStore>} The configured Redux store instance.
 */
export const store = configureStore({
    reducer: {
        auth: authReducer,
        service: serviceReducer,
        group: groupReducer,
        booking: bookingReducer,
        appointments: appointmentReducer,
    },
});

/**
 * Type for the root state of the Redux store.
 */
export type RootState = ReturnType<typeof store.getState>;

/**
 * Type for the Redux dispatch function.
 */
export type AppDispatch = typeof store.dispatch;

export const useAppDispatch = useDispatch.withTypes<AppDispatch>()
export const useAppSelector = useSelector.withTypes<RootState>()
import { getAuth } from '@/lib/auth'
import { headers } from 'next/headers'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import RecentAppointments from '@/components/dashboard/RecentAppointments'
import RecentAppointmentsLoading from '@/components/dashboard/RecentAppointmentsLoading'
import { searchPatientInCC } from '@/lib/clinicore'
import { Suspense } from 'react'

async function fetchUserAppointments(email?: string, phone?: string) {
    try {
        if (!email && !phone) {
            return []
        }

        // Search for patient in CliniCore
        const patient = await searchPatientInCC({ email, phone })

        if (!patient || !patient.appointments || patient.appointments.length === 0) {
            return []
        }

        // Fetch appointments by IDs
        const appointmentPromises = patient.appointments.map(async (id: number) => {
            try {
                const response = await fetch(`${process.env.CC_API_URL}/appointments/${id}`, {
                    headers: {
                        'Authorization': `Bearer ${process.env.CC_API_TOKEN}`,
                        'Content-Type': 'application/json',
                    },
                })

                if (!response.ok) {
                    return null
                }

                const data = await response.json()
                return data.appointment || data
            } catch (error) {
                console.error(`Failed to fetch appointment ${id}:`, error)
                return null
            }
        })

        const appointments = await Promise.all(appointmentPromises)
        return appointments.filter(apt => apt !== null)
    } catch (error) {
        console.error('Error fetching user appointments:', error)
        return []
    }
}

export default async function UserDashboard() {
    const session = await (await getAuth()).api.getSession({
        headers: await headers(),
    })

    const user = session?.user

    // Fetch user appointments
    const appointments = await fetchUserAppointments(user?.email, user?.phone)

    return (
        <div className="space-y-6">
            <div>
                <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
                <p className="text-muted-foreground">
                    Welcome back, {user?.name || user?.email}!
                </p>
            </div>

            {/* Recent Appointments Section */}
            <div className="grid gap-6 lg:grid-cols-3">
                <div className="lg:col-span-2">
                    <Suspense fallback={<RecentAppointmentsLoading />}>
                        <RecentAppointments appointments={appointments} />
                    </Suspense>
                </div>
                <div className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Quick Actions</CardTitle>
                            <CardDescription>
                                Common tasks and shortcuts
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <Button asChild className="w-full">
                                <Link href="/">
                                    Book New Appointment
                                </Link>
                            </Button>
                            <Button asChild variant="outline" className="w-full">
                                <Link href="/dashboard/appointments">
                                    View All Appointments
                                </Link>
                            </Button>
                            <Button asChild variant="outline" className="w-full">
                                <Link href="/dashboard/profile">
                                    Edit Profile
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                </div>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                <Card>
                    <CardHeader>
                        <CardTitle>My Appointments</CardTitle>
                        <CardDescription>
                            View and manage your upcoming appointments
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Button asChild className="w-full">
                            <Link href="/dashboard/appointments">
                                View Appointments
                            </Link>
                        </Button>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>Book Appointment</CardTitle>
                        <CardDescription>
                            Schedule a new appointment
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Button asChild className="w-full">
                            <Link href="/booking">
                                Book Now
                            </Link>
                        </Button>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>Profile Settings</CardTitle>
                        <CardDescription>
                            Update your personal information
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Button asChild variant="outline" className="w-full">
                            <Link href="/dashboard/profile">
                                Edit Profile
                            </Link>
                        </Button>
                    </CardContent>
                </Card>
            </div>

            {user?.role === 'admin' && (
                <Card className="border-orange-200 bg-orange-50">
                    <CardHeader>
                        <CardTitle className="text-orange-800">Admin Access</CardTitle>
                        <CardDescription className="text-orange-700">
                            You have administrator privileges
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Button asChild className="bg-orange-600 hover:bg-orange-700">
                            <Link href="/~">
                                Go to Admin Dashboard
                            </Link>
                        </Button>
                    </CardContent>
                </Card>
            )}
        </div>
    )
}

import { getAuth } from '@/lib/auth'
import { headers } from 'next/headers'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import AppointmentsList from '@/components/dashboard/AppointmentsList'
import { searchPatientInCC } from '@/lib/clinicore'

async function fetchUserAppointments(email?: string, phone?: string) {
    try {
        if (!email && !phone) {
            return []
        }

        // Search for patient in CliniCore
        const patient = await searchPatientInCC({ email, phone })

        if (!patient || !patient.appointments || patient.appointments.length === 0) {
            return []
        }

        // Fetch appointments by IDs
        const appointmentPromises = patient.appointments.map(async (id: number) => {
            try {
                const response = await fetch(`${process.env.CC_API_URL}/appointments/${id}`, {
                    headers: {
                        'Authorization': `Bearer ${process.env.CC_API_TOKEN}`,
                        'Content-Type': 'application/json',
                    },
                })

                if (!response.ok) {
                    return null
                }

                const data = await response.json()
                return data.appointment || data
            } catch (error) {
                console.error(`Failed to fetch appointment ${id}:`, error)
                return null
            }
        })

        const appointments = await Promise.all(appointmentPromises)
        return appointments.filter(apt => apt !== null)
    } catch (error) {
        console.error('Error fetching user appointments:', error)
        return []
    }
}

export default async function UserAppointments() {
    const session = await (await getAuth()).api.getSession({
        headers: await headers(),
    })

    const user = session?.user

    // Fetch user appointments
    const appointments = await fetchUserAppointments(user?.email, user?.phone)

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">My Appointments</h1>
                    <p className="text-muted-foreground">
                        View and manage your upcoming appointments
                    </p>
                </div>
                <Button asChild>
                    <Link href="/">
                        Book New Appointment
                    </Link>
                </Button>
            </div>

            <AppointmentsList initialAppointments={appointments} />
        </div>
    )
}

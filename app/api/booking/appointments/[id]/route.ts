import { NextRequest, NextResponse } from "next/server";
import { ccRequest } from "@/lib/clinicore";

/**
 * Cancel an appointment in CliniCore
 */
async function cancelAppointmentInCC(id: number): Promise<unknown> {
  try {
    const payload = {
      canceledWhy: "cancelled by user",
    };
    
    const response = await ccRequest(`/appointments/${id}`, {
      method: "PUT",
      body: { appointment: payload },
    });
    
    return response;
  } catch (error) {
    console.error(`Failed to cancel appointment ${id}:`, error);
    throw error;
  }
}

/**
 * DELETE handler for cancelling appointments
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const appointmentId = parseInt(params.id);
    
    if (isNaN(appointmentId)) {
      return NextResponse.json({
        status: 400,
        message: "Ungültige Termin-ID",
      }, { status: 400 });
    }

    // Get optional cancellation reason from request body
    let reason = "cancelled by user";
    try {
      const body = await req.json();
      if (body.reason) {
        reason = body.reason;
      }
    } catch {
      // Body is optional, use default reason
    }

    // Cancel appointment in CliniCore
    const cancelledAppointment = await cancelAppointmentInCC(appointmentId);

    return NextResponse.json({
      status: 200,
      message: "Termin erfolgreich storniert",
      appointment: cancelledAppointment,
    });
  } catch (error: any) {
    console.error('Error cancelling appointment:', error);
    
    return NextResponse.json({
      status: 500,
      message: "Fehler beim Stornieren des Termins. Bitte versuchen Sie es später noch einmal.",
      error: error?.message || error,
    }, { status: 500 });
  }
}

"use client"
import Link from 'next/link'
import { NavUser } from './nav-user'
// Define the user type based on what we get from the session
type SessionUser = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date
  updatedAt: Date
  role?: string | null
}
import { Button } from './ui/button'
import { useState } from 'react'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { Settings, Users, Calendar, User, Plus } from 'lucide-react';

/**
 * TopBar component displays the navigation bar with user info and role-based navigation.
 * @param user - The current user
 * @returns JSX.Element
 */
export default function TopBar({ user }: { user: SessionUser }) {
    const [isSyncing, setIsSyncing] = useState(false)
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
    const pathname = usePathname()
    const isAdminRoute = pathname.startsWith('/~')
    const isUserRoute = pathname.startsWith('/dashboard')
    const isBookingRoute = pathname === '/'
    const isProfileRoute = pathname === '/dashboard/profile'
    const isAdmin = user.role === 'admin'
    const syncData = () => {
        setIsSyncing(true)
        fetch("/api/sync")
            .then(res => res.json())
            .then(() => {
                setIsSyncing(false)
            })
            .catch(() => {
                setIsSyncing(false)
            })
    }
    return (
        <nav className="border-b border-gray-200 bg-white">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div className="flex h-16 justify-between">
                    <div className="flex">
                        <div className="flex shrink-0 items-center">
                            <Link href={isAdmin ? "/~" : "/dashboard"}>
                                <Image
                                    alt="Wolanin MD Aesthetics"
                                    src="https://tailwindcss.com/plus-assets/img/logos/mark.svg?color=indigo&shade=600"
                                    className="block h-8 w-auto lg:hidden"
                                    width={32}
                                    height={32}
                                    unoptimized
                                />
                            </Link>
                            <Link href={isAdmin ? "/~" : "/dashboard"}>
                                <Image
                                    alt="Wolanin MD Aesthetics"
                                    src="https://tailwindcss.com/plus-assets/img/logos/mark.svg?color=indigo&shade=600"
                                    className="hidden h-8 w-auto lg:block"
                                    width={32}
                                    height={32}
                                    unoptimized
                                />
                            </Link>
                        </div>

                        {/* Navigation Links */}
                        <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                            {/* Dashboard Link - visible to all authenticated users */}
                            <Link
                                href="/dashboard"
                                className={`inline-flex items-center px-1 pt-1 text-sm font-medium ${
                                    isUserRoute && !isAdminRoute
                                        ? 'border-b-2 border-indigo-500 text-gray-900'
                                        : 'text-gray-500 hover:text-gray-700'
                                }`}
                            >
                                <Calendar className="w-4 h-4 mr-2" />
                                Dashboard
                            </Link>

                            {/* Profile Link - visible to all authenticated users */}
                            <Link
                                href="/dashboard/profile"
                                className={`inline-flex items-center px-1 pt-1 text-sm font-medium ${
                                    isProfileRoute
                                        ? 'border-b-2 border-indigo-500 text-gray-900'
                                        : 'text-gray-500 hover:text-gray-700'
                                }`}
                            >
                                <User className="w-4 h-4 mr-2" />
                                Profile
                            </Link>

                            {/* Book New Appointment Link - visible to all authenticated users */}
                            <Link
                                href="/"
                                className={`inline-flex items-center px-1 pt-1 text-sm font-medium ${
                                    isBookingRoute
                                        ? 'border-b-2 border-indigo-500 text-gray-900'
                                        : 'text-gray-500 hover:text-gray-700'
                                }`}
                            >
                                <Plus className="w-4 h-4 mr-2" />
                                Book New Appointment
                            </Link>

                            {/* Admin Dashboard Link - only visible to admins */}
                            {isAdmin && (
                                <Link
                                    href="/~"
                                    className={`inline-flex items-center px-1 pt-1 text-sm font-medium ${
                                        isAdminRoute
                                            ? 'border-b-2 border-indigo-500 text-gray-900'
                                            : 'text-gray-500 hover:text-gray-700'
                                    }`}
                                >
                                    <Settings className="w-4 h-4 mr-2" />
                                    Admin Dashboard
                                </Link>
                            )}
                        </div>

                        {/* Mobile menu button */}
                        <div className="sm:hidden">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                                className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
                            >
                                <span className="sr-only">Open main menu</span>
                                {/* Menu icon */}
                                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                </svg>
                            </Button>
                        </div>
                    </div>
                    <div className="hidden sm:ml-6 sm:flex sm:items-center">
                        {/* Show sync button only on admin routes */}
                        {isAdminRoute && (
                            <>
                                <span className="text-sm text-red-500 mr-4 animate-pulse">Can&apos;t find a service? </span>
                                <Button variant="destructive" className="mr-8" onClick={syncData} disabled={isSyncing}>
                                    {isSyncing ? "Syncing..." : "Sync Data from CliniCore"}
                                </Button>
                            </>
                        )}
                        <NavUser
                            user={{
                                name: user.name,
                                email: user.email,
                                avatar: user.image || "",
                            }}
                        />
                    </div>
                </div>
            </div>

            {/* Mobile menu */}
            {isMobileMenuOpen && (
                <div className="sm:hidden">
                    <div className="pt-2 pb-3 space-y-1 border-t border-gray-200">
                        {/* Dashboard Link */}
                        <Link
                            href="/dashboard"
                            className={`block pl-3 pr-4 py-2 text-base font-medium ${
                                isUserRoute && !isAdminRoute
                                    ? 'text-indigo-700 bg-indigo-50 border-r-4 border-indigo-500'
                                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                            }`}
                            onClick={() => setIsMobileMenuOpen(false)}
                        >
                            Dashboard
                        </Link>

                        {/* Profile Link */}
                        <Link
                            href="/dashboard/profile"
                            className={`block pl-3 pr-4 py-2 text-base font-medium ${
                                isProfileRoute
                                    ? 'text-indigo-700 bg-indigo-50 border-r-4 border-indigo-500'
                                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                            }`}
                            onClick={() => setIsMobileMenuOpen(false)}
                        >
                            Profile
                        </Link>

                        {/* Book New Appointment Link */}
                        <Link
                            href="/"
                            className={`block pl-3 pr-4 py-2 text-base font-medium ${
                                isBookingRoute
                                    ? 'text-indigo-700 bg-indigo-50 border-r-4 border-indigo-500'
                                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                            }`}
                            onClick={() => setIsMobileMenuOpen(false)}
                        >
                            Book New Appointment
                        </Link>

                        {/* Admin Dashboard Link - only visible to admins */}
                        {isAdmin && (
                            <Link
                                href="/~"
                                className={`block pl-3 pr-4 py-2 text-base font-medium ${
                                    isAdminRoute
                                        ? 'text-indigo-700 bg-indigo-50 border-r-4 border-indigo-500'
                                        : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                                }`}
                                onClick={() => setIsMobileMenuOpen(false)}
                            >
                                Admin Dashboard
                            </Link>
                        )}
                    </div>
                </div>
            )}
        </nav>
    )
}

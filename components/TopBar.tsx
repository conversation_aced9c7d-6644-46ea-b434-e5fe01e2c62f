"use client"
import Link from 'next/link'
import { NavUser } from './nav-user'
// Define the user type based on what we get from the session
type SessionUser = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  createdAt: Date
  updatedAt: Date
  role?: string | null
}
import { Button } from './ui/button'
import { useState } from 'react'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { Settings, Users } from 'lucide-react';

/**
 * TopBar component displays the navigation bar with user info and role-based navigation.
 * @param user - The current user
 * @returns JSX.Element
 */
export default function TopBar({ user }: { user: SessionUser }) {
    const [isSyncing, setIsSyncing] = useState(false)
    const pathname = usePathname()
    const isAdminRoute = pathname.startsWith('/~')
    const isUserRoute = pathname.startsWith('/dashboard')
    const isAdmin = user.role === 'admin'
    const syncData = () => {
        setIsSyncing(true)
        fetch("/api/sync")
            .then(res => res.json())
            .then(() => {
                setIsSyncing(false)
            })
            .catch(() => {
                setIsSyncing(false)
            })
    }
    return (
        <nav className="border-b border-gray-200 bg-white">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div className="flex h-16 justify-between">
                    <div className="flex">
                        <div className="flex shrink-0 items-center">
                            <Link href={isAdmin ? "/~" : "/dashboard"}>
                                <Image
                                    alt="Wolanin MD Aesthetics"
                                    src="https://tailwindcss.com/plus-assets/img/logos/mark.svg?color=indigo&shade=600"
                                    className="block h-8 w-auto lg:hidden"
                                    width={32}
                                    height={32}
                                    unoptimized
                                />
                            </Link>
                            <Link href={isAdmin ? "/~" : "/dashboard"}>
                                <Image
                                    alt="Wolanin MD Aesthetics"
                                    src="https://tailwindcss.com/plus-assets/img/logos/mark.svg?color=indigo&shade=600"
                                    className="hidden h-8 w-auto lg:block"
                                    width={32}
                                    height={32}
                                    unoptimized
                                />
                            </Link>
                        </div>

                        {/* Navigation Links */}
                        <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                            {isAdmin && (
                                <>
                                    <Link
                                        href="/~"
                                        className={`inline-flex items-center px-1 pt-1 text-sm font-medium ${
                                            isAdminRoute
                                                ? 'border-b-2 border-indigo-500 text-gray-900'
                                                : 'text-gray-500 hover:text-gray-700'
                                        }`}
                                    >
                                        <Settings className="w-4 h-4 mr-2" />
                                        Admin Dashboard
                                    </Link>
                                    <Link
                                        href="/dashboard"
                                        className={`inline-flex items-center px-1 pt-1 text-sm font-medium ${
                                            isUserRoute
                                                ? 'border-b-2 border-indigo-500 text-gray-900'
                                                : 'text-gray-500 hover:text-gray-700'
                                        }`}
                                    >
                                        <Users className="w-4 h-4 mr-2" />
                                        User Dashboard
                                    </Link>
                                </>
                            )}
                        </div>
                    </div>
                    <div className="hidden sm:ml-6 sm:flex sm:items-center">
                        {/* Show sync button only on admin routes */}
                        {isAdminRoute && (
                            <>
                                <span className="text-sm text-red-500 mr-4 animate-pulse">Can&apos;t find a service? </span>
                                <Button variant="destructive" className="mr-8" onClick={syncData} disabled={isSyncing}>
                                    {isSyncing ? "Syncing..." : "Sync Data from CliniCore"}
                                </Button>
                            </>
                        )}
                        <NavUser
                            user={{
                                name: user.name,
                                email: user.email,
                                avatar: user.image || "",
                            }}
                        />
                    </div>
                </div>
            </div>
        </nav>
    )
}

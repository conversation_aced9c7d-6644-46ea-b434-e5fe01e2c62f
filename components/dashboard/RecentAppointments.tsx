import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { Calendar, Clock, MapPin } from 'lucide-react'
import { format, isAfter, formatDistanceToNow, parseISO } from 'date-fns'
import { de } from 'date-fns/locale'

interface Appointment {
  id: number
  title: string
  startsAt: string
  endsAt: string
  canceledWhy?: string | null
  services?: Array<{
    id: number
    name: string
  }>
}

interface RecentAppointmentsProps {
  appointments: Appointment[]
}

/**
 * Component to display recent upcoming appointments on the dashboard
 */
export default function RecentAppointments({ appointments }: RecentAppointmentsProps) {
  // Filter and sort appointments
  const upcomingAppointments = appointments
    .filter(appointment => {
      // Filter out cancelled appointments
      if (appointment.canceledWhy) return false

      // Filter out past appointments
      const appointmentDate = parseISO(appointment.startsAt)
      return isAfter(appointmentDate, new Date())
    })
    .sort((a, b) => parseISO(a.startsAt).getTime() - parseISO(b.startsAt).getTime())
    .slice(0, 3) // Show only the next 3 appointments

  if (upcomingAppointments.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Upcoming Appointments</CardTitle>
          <CardDescription>
            Your next appointments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-4">
              No upcoming appointments
            </p>
            <Button asChild size="sm">
              <Link href="/">
                Book New Appointment
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Upcoming Appointments</CardTitle>
            <CardDescription>
              Your next {upcomingAppointments.length} appointment{upcomingAppointments.length !== 1 ? 's' : ''}
            </CardDescription>
          </div>
          <Button asChild variant="outline" size="sm">
            <Link href="/dashboard/appointments">
              View All
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {upcomingAppointments.map((appointment) => {
            const appointmentDate = parseISO(appointment.startsAt)
            const serviceName = appointment.services && appointment.services.length > 0
              ? appointment.services[0].name
              : appointment.title || 'Appointment'

            return (
              <div key={appointment.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-sm">{serviceName}</h4>
                  <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {format(appointmentDate, 'dd.MM.yyyy', { locale: de })}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {format(appointmentDate, 'HH:mm', { locale: de })}
                    </span>
                  </div>
                </div>
                <div className="text-xs text-muted-foreground">
                  {formatDistanceToNow(appointmentDate, { addSuffix: true, locale: de })}
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}

import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { Calendar, Clock, MapPin } from 'lucide-react'
import moment from 'moment'
import 'moment/locale/de'

// Set moment locale to German
moment.locale('de')

interface Appointment {
  id: number
  title: string
  startsAt: string
  endsAt: string
  canceledWhy?: string | null
  services?: Array<{
    id: number
    name: string
  }>
}

interface RecentAppointmentsProps {
  appointments: Appointment[]
}

/**
 * Component to display recent upcoming appointments on the dashboard
 */
export default function RecentAppointments({ appointments }: RecentAppointmentsProps) {
  // Filter and sort appointments
  const upcomingAppointments = appointments
    .filter(appointment => {
      // Filter out cancelled appointments
      if (appointment.canceledWhy) return false
      
      // Filter out past appointments
      const appointmentDate = moment(appointment.startsAt)
      return appointmentDate.isAfter(moment())
    })
    .sort((a, b) => moment(a.startsAt).diff(moment(b.startsAt)))
    .slice(0, 3) // Show only the next 3 appointments

  if (upcomingAppointments.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Upcoming Appointments</CardTitle>
          <CardDescription>
            Your next appointments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-4">
              No upcoming appointments
            </p>
            <Button asChild size="sm">
              <Link href="/">
                Book New Appointment
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Upcoming Appointments</CardTitle>
            <CardDescription>
              Your next {upcomingAppointments.length} appointment{upcomingAppointments.length !== 1 ? 's' : ''}
            </CardDescription>
          </div>
          <Button asChild variant="outline" size="sm">
            <Link href="/dashboard/appointments">
              View All
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {upcomingAppointments.map((appointment) => {
            const appointmentDate = moment(appointment.startsAt)
            const serviceName = appointment.services && appointment.services.length > 0 
              ? appointment.services[0].name 
              : appointment.title || 'Appointment'

            return (
              <div key={appointment.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-sm">{serviceName}</h4>
                  <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {appointmentDate.format('DD.MM.YYYY')}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {appointmentDate.format('HH:mm')}
                    </span>
                  </div>
                </div>
                <div className="text-xs text-muted-foreground">
                  {appointmentDate.fromNow()}
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}

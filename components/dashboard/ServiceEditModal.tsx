import React, { useState, ChangeEvent, forwardRef, useImperativeHandle } from "react";
import { useAppDispatch } from "@/store";
import { editService } from "@/store/serviceSlice";
import Image from "next/image";

/**
 * Props for ServiceEditModal.
 * @typedef {Object} ServiceEditModalProps
 * @property {IStoreService} service - The service to edit
 * @property {(success: boolean) => void} onSubmit - Called after submit (true if success, false if error)
 */
interface ServiceEditModalProps {
    service: IStoreService;
    onSubmit: (success: boolean) => void;
}

/**
 * Exposes a submit() method via ref for parent modal to trigger form submission.
 * @param {ServiceEditModalProps} props
 * @param {React.Ref<{ submit: () => void }>} ref
 */
const ServiceEditModal = forwardRef<{ submit: () => void }, ServiceEditModalProps>(({ service, onSubmit }, ref) => {
    const dispatch = useAppDispatch();
    const [displayName, setDisplayName] = useState(service.displayName ?? service.name);
    const [displayDescription, setDisplayDescription] = useState(service.displayDescription ?? service.description ?? "");
    const [price, setPrice] = useState(service.price);
    const [image, setImage] = useState(service.image ?? "");
    const [uploading, setUploading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Expose submit method to parent
    useImperativeHandle(ref, () => ({
        submit: () => handleSubmit()
    }));

    const handleImageChange = async (e: ChangeEvent<HTMLInputElement>) => {
        if (!e.target.files || e.target.files.length === 0) return;
        const file = e.target.files[0];
        setUploading(true);
        setError(null);
        try {
            // Pass the previous image key as oldKey if it exists
            const oldKey = image && image.startsWith('service/') ? image : undefined;
            const formData = new FormData();
            formData.append("file", file);
            formData.append("id", service.id);
            if (oldKey) formData.append("oldKey", oldKey);
            // Directly call the endpoint for custom FormData
            const res = await fetch("/api/service/image", {
                method: "POST",
                body: formData,
            });
            if (!res.ok) throw new Error("Failed to upload image");
            const data: { url: string } = await res.json();
            setImage(data.url);
        } catch (err: unknown) {
            setError(err instanceof Error ? err.message : "Failed to upload image");
        } finally {
            setUploading(false);
        }
    };

    const handleSubmit = async () => {
        setError(null);
        try {
            await dispatch(editService({
                id: service.id,
                displayName,
                displayDescription,
                price,
                image,
            })).unwrap();
            onSubmit(true);
        } catch (err: unknown) {
            setError(err instanceof Error ? err.message : "Failed to update service");
            onSubmit(false);
        }
    };

    return (
        <form className="space-y-4" onSubmit={e => { e.preventDefault(); handleSubmit(); }}>
            <div>
                <label className="block font-medium mb-1">Display Name</label>
                <input
                    className="w-full border rounded px-2 py-1"
                    value={displayName}
                    onChange={e => setDisplayName(e.target.value)}
                    required
                />
            </div>
            <div>
                <label className="block font-medium mb-1">Display Description</label>
                <textarea
                    className="w-full border rounded px-2 py-1"
                    value={displayDescription}
                    onChange={e => setDisplayDescription(e.target.value)}
                    rows={3}
                />
            </div>
            <div>
                <label className="block font-medium mb-1">Price</label>
                <input
                    className="w-full border rounded px-2 py-1"
                    type="number"
                    min={0}
                    value={price}
                    onChange={e => setPrice(Number(e.target.value))}
                    required
                />
            </div>
            <div>
                <label className="block font-medium mb-1">Image</label>
                {image && (
                    <div className="mb-2">
                        <Image src={image} alt="Service" width={120} height={120} className="rounded object-cover" />
                    </div>
                )}
                <input type="file" accept="image/*" onChange={handleImageChange} disabled={uploading} />
                {uploading && <div className="text-sm text-gray-500">Uploading...</div>}
            </div>
            {error && <div className="text-red-500 text-sm">{error}</div>}
        </form>
    );
});
ServiceEditModal.displayName = "ServiceEditModal";

export default ServiceEditModal; 